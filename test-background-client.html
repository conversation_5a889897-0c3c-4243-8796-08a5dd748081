<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 Background Question Generation Test</h1>
    
    <div class="test-section">
        <h2>1. Role Validation Test</h2>
        <input type="text" id="roleInput" placeholder="Enter role (e.g., Account Manager)" value="Account Manager">
        <button onclick="testRoleValidation()">Validate Role</button>
        <div id="roleStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Background Generation Monitoring</h2>
        <button onclick="startMonitoring()" id="monitorBtn">Start Monitoring</button>
        <button onclick="stopMonitoring()" id="stopBtn" disabled>Stop Monitoring</button>
        <div id="monitorStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Question Loading Test</h2>
        <input type="text" id="sectionInput" placeholder="Section (e.g., Essentials)" value="Essentials">
        <button onclick="testQuestionLoading()">Load Questions</button>
        <div id="questionStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log"></div>
    </div>

    <script>
        let monitoringInterval = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.className = type;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function testRoleValidation() {
            const role = document.getElementById('roleInput').value.trim();
            if (!role) {
                showStatus('roleStatus', 'Please enter a role', 'error');
                return;
            }
            
            log(`🔍 Testing role validation for: ${role}`);
            showStatus('roleStatus', 'Validating role...', 'info');
            
            try {
                const response = await fetch('/api/validate-role', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ role })
                });
                
                const result = await response.json();
                
                if (result.isValid) {
                    showStatus('roleStatus', `✅ Role "${role}" is valid! Background generation should have started.`, 'success');
                    log(`✅ Role validation successful for: ${role}`, 'success');
                    
                    // Auto-start monitoring
                    setTimeout(() => startMonitoring(), 1000);
                } else {
                    showStatus('roleStatus', `❌ Role "${role}" is not valid`, 'error');
                    log(`❌ Role validation failed for: ${role}`, 'error');
                }
            } catch (error) {
                showStatus('roleStatus', `Error: ${error.message}`, 'error');
                log(`❌ Role validation error: ${error.message}`, 'error');
            }
        }
        
        async function startMonitoring() {
            const role = document.getElementById('roleInput').value.trim();
            if (!role) {
                showStatus('monitorStatus', 'Please enter a role first', 'error');
                return;
            }
            
            document.getElementById('monitorBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            log(`📊 Starting background generation monitoring for: ${role}`);
            showStatus('monitorStatus', 'Monitoring background generation...', 'info');
            
            monitoringInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/background-generation-status/${encodeURIComponent(role)}`);
                    const status = await response.json();
                    
                    if (status.found) {
                        const progress = status.completionPercentage || 0;
                        const completed = status.completed ? '✅ Completed' : '⏳ In Progress';
                        
                        showStatus('monitorStatus', 
                            `${completed} - Progress: ${progress}%<br>
                             Sections: ${JSON.stringify(status.sections, null, 2)}`, 
                            status.completed ? 'success' : 'info');
                        
                        log(`📊 Background generation progress: ${progress}% (${completed})`);
                        
                        if (status.completed) {
                            log(`🎉 Background generation completed for: ${role}`, 'success');
                            stopMonitoring();
                        }
                    } else {
                        showStatus('monitorStatus', '❌ No background generation found', 'warning');
                        log(`❌ No background generation found for: ${role}`, 'warning');
                    }
                } catch (error) {
                    log(`❌ Monitoring error: ${error.message}`, 'error');
                }
            }, 3000);
        }
        
        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            document.getElementById('monitorBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            log('🛑 Stopped background generation monitoring');
            showStatus('monitorStatus', 'Monitoring stopped', 'info');
        }
        
        async function testQuestionLoading() {
            const role = document.getElementById('roleInput').value.trim();
            const section = document.getElementById('sectionInput').value.trim();
            
            if (!role || !section) {
                showStatus('questionStatus', 'Please enter both role and section', 'error');
                return;
            }
            
            log(`❓ Testing question loading for: ${role} - ${section}`);
            showStatus('questionStatus', 'Loading questions...', 'info');
            
            const startTime = Date.now();
            
            try {
                // First get framework
                const frameworkResponse = await fetch('/api/generate-framework', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ role, email: '<EMAIL>' })
                });
                
                if (!frameworkResponse.ok) {
                    throw new Error('Failed to generate framework');
                }
                
                const framework = await frameworkResponse.json();
                
                // Then get questions
                const questionsResponse = await fetch('/api/generate-quiz', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ role, section, framework, email: '<EMAIL>' })
                });
                
                if (!questionsResponse.ok) {
                    throw new Error('Failed to generate questions');
                }
                
                const questions = await questionsResponse.json();
                const duration = Date.now() - startTime;
                
                const speedIndicator = duration < 3000 ? '🚀 FAST' : '🐌 SLOW';
                const speedClass = duration < 3000 ? 'success' : 'warning';
                
                showStatus('questionStatus', 
                    `${speedIndicator} - Loaded ${questions.length} questions in ${duration}ms<br>
                     Sample: ${questions[0]?.question || 'No questions'}`, 
                    speedClass);
                
                log(`✅ Question loading completed: ${questions.length} questions in ${duration}ms (${speedIndicator})`, speedClass.replace('warning', 'info'));
                
            } catch (error) {
                showStatus('questionStatus', `Error: ${error.message}`, 'error');
                log(`❌ Question loading error: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        log('🧪 Background Generation Test Page Loaded', 'info');
    </script>
</body>
</html>
