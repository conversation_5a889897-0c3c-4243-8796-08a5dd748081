// Test script for background question generation system
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testBackgroundGeneration() {
  console.log('🧪 Testing Background Question Generation System\n');

  try {
    // Test 1: Role validation (should trigger background generation)
    console.log('1️⃣ Testing role validation...');
    const roleResponse = await axios.post(`${BASE_URL}/api/validate-role`, {
      role: 'Account Manager'
    });
    
    console.log('✅ Role validation response:', roleResponse.data);
    
    if (roleResponse.data.isValid) {
      console.log('🚀 Background generation should have been triggered\n');
      
      // Wait a moment for background generation to start
      await sleep(2000);
      
      // Test 2: Check background generation status
      console.log('2️⃣ Checking background generation status...');
      const statusResponse = await axios.get(`${BASE_URL}/api/background-generation-status/Account Manager`);
      console.log('📊 Background generation status:', statusResponse.data);
      
      if (statusResponse.data.found) {
        console.log('✅ Background generation is running\n');
        
        // Test 3: Wait for completion and monitor progress
        console.log('3️⃣ Monitoring background generation progress...');
        await monitorBackgroundGeneration('Account Manager');
        
        // Test 4: Try to get questions (should be instant if background generation completed)
        console.log('4️⃣ Testing question retrieval...');
        await testQuestionRetrieval('Account Manager', 'Essentials');
        
      } else {
        console.log('❌ Background generation not found\n');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

async function monitorBackgroundGeneration(role) {
  const maxWait = 60000; // 60 seconds
  const pollInterval = 3000; // 3 seconds
  let elapsed = 0;
  
  while (elapsed < maxWait) {
    try {
      const response = await axios.get(`${BASE_URL}/api/background-generation-status/${encodeURIComponent(role)}`);
      const status = response.data;
      
      if (status.found) {
        console.log(`⏳ Progress: ${status.completionPercentage}% - Completed: ${status.completed}`);
        
        if (status.completed) {
          console.log('🎉 Background generation completed!\n');
          return true;
        }
      } else {
        console.log('❌ Background generation not found');
        return false;
      }
      
    } catch (error) {
      console.error('Error checking status:', error.message);
    }
    
    await sleep(pollInterval);
    elapsed += pollInterval;
  }
  
  console.log('⏰ Timeout waiting for background generation\n');
  return false;
}

async function testQuestionRetrieval(role, section) {
  try {
    const startTime = Date.now();
    
    // First, we need a framework to generate questions
    console.log('📋 Generating framework first...');
    const frameworkResponse = await axios.post(`${BASE_URL}/api/generate-framework`, {
      role: role,
      email: '<EMAIL>'
    });
    
    if (frameworkResponse.status === 200) {
      console.log('✅ Framework generated');
      
      // Now try to get questions
      console.log('❓ Requesting questions...');
      const questionsResponse = await axios.post(`${BASE_URL}/api/generate-quiz`, {
        role: role,
        section: section,
        framework: frameworkResponse.data,
        email: '<EMAIL>'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (questionsResponse.status === 200) {
        const questions = questionsResponse.data;
        console.log(`✅ Retrieved ${questions.length} questions in ${duration}ms`);
        
        if (duration < 3000) {
          console.log('🚀 FAST LOADING! Background generation likely worked');
        } else {
          console.log('🐌 Slow loading - may have fallen back to progressive generation');
        }
        
        console.log('📝 Sample question:', questions[0]?.question || 'No questions found');
      } else {
        console.log('❌ Failed to retrieve questions');
      }
    } else {
      console.log('❌ Failed to generate framework');
    }
    
  } catch (error) {
    console.error('❌ Question retrieval failed:', error.message);
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the test
if (require.main === module) {
  testBackgroundGeneration().then(() => {
    console.log('🏁 Test completed');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { testBackgroundGeneration };
